<view><back vue-id="8dd740cc-1" showBackText="{{false}}" showBackIcon="{{false}}" showBackLeft="{{false}}" showHomeIcon="{{false}}" customClass="bg-gradual-blue text-white" title="首页" bind:__l="__l"></back><view hidden="{{!(!isLoading)}}" class="{{[(isLargeScreen)?'large-screen-container':'']}}"><view class="{{['cu-bar','bg-white','search',(isLargeScreen)?'large-screen-search':'']}}"><view data-event-opts="{{[['tap',[['goTo',['city/city?first_visit=1']]]]]}}" class="action" style="{{(isLargeScreen?'font-size: 32rpx;':'font-size: 27rpx; max-width: 33.3333%')}}" bindtap="__e"><text class="cuIcon-location text-blue" style="margin:0;"></text><text class="text-blue text-cut">{{currentCity.name}}</text></view><view data-event-opts="{{[['tap',[['goTo',['exam/exam']]]]]}}" class="action" style="{{(isLargeScreen?'font-size: 32rpx;':'font-size: 27rpx; max-width: 33.3333%')}}" bindtap="__e"><text class="cuIcon-group text-blue" style="margin-right:6rpx;"></text><text class="text-blue text-cut">{{currentExam.name}}</text></view><view data-event-opts="{{[['tap',[['goTo',['profession/profession']]]]]}}" class="action" style="{{(isLargeScreen?'font-size: 32rpx;':'font-size: 27rpx; max-width: 33.3333%')}}" bindtap="__e"><text class="cuIcon-read text-blue" style="margin-right:6rpx;"></text><text class="text-blue text-cut">{{currentProfession.name||'选择专业'}}</text></view></view><swiper class="{{['swiper','screen-swiper','square-dot','margin-top-xs',(isLargeScreen)?'large-screen-swiper':'']}}" style="{{$root.m0}}" indicator-dots="{{true}}" circular="{{true}}" autoplay="{{true}}" interval="5000" duration="500"><block wx:for="{{images}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item data-event-opts="{{[['tap',[['goTo',['$0'],[[['images','',index,'target_url']]]]]]]}}" bindtap="__e"><image class="swiper-image" style="{{$root.m1}}" src="{{item.img_url}}"></image></swiper-item></block></swiper><block wx:if="{{!appIsAudit&&showIndexShareName&&(appPlatform==20||appPlatform==21)}}"><view class="cu-bar bg-white margin-top-xs no-border" style="font-size:50rpx;display:none;"><view class="action" style="font-size:48rpx;"><text class="cuIcon-notificationfill text-blue" style="font-size:50rpx;"></text><text class="text-blue text-df">{{showIndexShareName}}</text></view><view class="action"><button class="cu-btn margin-tb-sm bg-blue shadow" open-type="share">分享<text class="cuIcon-share" style="margin-left:6rpx;"></text></button></view></view></block><block wx:if="{{examTime.show}}"><view class="{{['exam-countdown',(isLargeScreen)?'large-screen-countdown':'']}}" style="{{$root.m2}}"><view class="countdown-content"><view class="countdown-header"><text class="cuIcon-time text-blue"></text><text class="countdown-title">考试倒计时</text></view><view class="countdown-info"><view class="countdown-date">{{"考试日期："+examTime.exam_date}}</view><view class="countdown-timer"><text class="countdown-unit">还剩</text><text class="countdown-days">{{examTime.exam_remain}}</text><text class="countdown-unit">天</text></view></view></view></view></block><view class="{{['cu-bar','bg-white','margin-top-xs',(isLargeScreen)?'large-screen-bar':'']}}" style="{{$root.m3}}"><view class="action sub-title"><text class="text-xl text-bold text-blue text-shadow">学习中心</text><text class="text-ABC text-blue">Study</text></view><view class="action"><block wx:if="{{isLargeScreen}}"><button data-event-opts="{{[['tap',[['goTo',['/pages/pc-test/pc-test']]]]]}}" class="cu-btn margin-tb-sm bg-blue shadow" bindtap="__e">大屏测试<text class="cuIcon-settings" style="margin-left:6rpx;"></text></button></block><block wx:else><button class="cu-btn margin-tb-sm bg-blue shadow" open-type="share">分享<text class="cuIcon-share" style="margin-left:6rpx;"></text></button></block></view></view><view class="{{['modern-grid','bg-white','radius','shadow-warp',(isLargeScreen)?'large-screen-grid':'']}}" style="{{$root.m4}}"><block wx:for="{{menuList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goTo',['$0'],[[['menuList','',index,'page_uri']]]]]]]}}" class="{{['modern-grid-item',(isLargeScreen)?'large-screen-grid-item':'']}}" style="{{$root.m5}}" bindtap="__e"><image class="modern-grid-icon" src="{{item.icon}}" mode="aspectFit"></image><text class="modern-grid-text">{{item.name}}</text></view></block></view><block wx:if="{{!appIsAudit}}"><view class="{{['cu-bar','bg-white','margin-top-xs',(isLargeScreen)?'large-screen-bar':'']}}" style="{{$root.m6}}"><view class="action sub-title"><text class="text-xl text-bold text-blue text-shadow">最新资讯</text><text class="text-ABC text-blue">News</text></view><view data-event-opts="{{[['tap',[['goTo',['./article/list']]]]]}}" class="action" bindtap="__e"><button class="cu-btn bg-blue margin-tb-sm shadow" data-target="Modal">更多<text class="cuIcon-moreandroid" style="margin-left:5rpx;"></text></button></view></view></block><block wx:if="{{!appIsAudit||appPlatform!=20&&appPlatform!=21}}"><view class="{{['cu-card','article','no-card','solid-bottom',(isLargeScreen)?'large-screen-article':'']}}" style="{{$root.m7}}"><block wx:for="{{articleList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goTo',['./article/detail?id='+item.id]]]]]}}" class="cu-item shadow" bindtap="__e"><view class="content"><view><image class="radius" style="height:120rpx;width:120rpx;" src="{{item.thumb}}" mode="aspectFit"></image></view><view class="desc" style="justify-content:space-around;margin-left:10rpx;"><view class="text-black text-df">{{item.title}}</view><view class="flex justify-between"><view class="text-gray text-sm">{{item.cate_name+" · "+item.create_date}}</view><view class="text-gray text-sm padding-right text-shadow">{{item.page_view+' 阅读'}}</view></view></view></view></view></block></view></block></view></view>