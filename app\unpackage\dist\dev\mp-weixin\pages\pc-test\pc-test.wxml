<view class="data-v-1bd04540"><view class="test-container data-v-1bd04540"><view class="test-header data-v-1bd04540"><text class="test-title data-v-1bd04540">PC端适配测试页面</text></view><view class="test-info data-v-1bd04540"><view class="info-item data-v-1bd04540"><text class="info-label data-v-1bd04540">当前平台：</text><text class="info-value data-v-1bd04540">{{$root.m0}}</text></view><view class="info-item data-v-1bd04540"><text class="info-label data-v-1bd04540">窗口宽度：</text><text class="info-value data-v-1bd04540">{{windowWidth+"px"}}</text></view><view class="info-item data-v-1bd04540"><text class="info-label data-v-1bd04540">宫格列数：</text><text class="info-value data-v-1bd04540">{{gridColumns+"列"}}</text></view><view class="info-item data-v-1bd04540"><text class="info-label data-v-1bd04540">容器宽度：</text><text class="info-value data-v-1bd04540">{{containerWidth}}</text></view></view><view class="test-grid data-v-1bd04540" style="{{(isPC?'width: '+containerWidth+'; margin: 0 auto;':'')}}"><block wx:for="{{12}}" wx:for-item="n" wx:for-index="__i0__" wx:key="*this"><view class="grid-item data-v-1bd04540" style="{{(isPC?'width: '+100/gridColumns+'%;':'width: 33.33%;')}}"><view class="grid-content data-v-1bd04540"><text class="data-v-1bd04540">{{"项目 "+n}}</text></view></view></block></view><view class="test-buttons data-v-1bd04540"><button data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="test-btn data-v-1bd04540" bindtap="__e">返回首页</button></view></view></view>