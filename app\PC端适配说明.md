# 首页PC端适配说明

## 概述
为贝壳刷题应用的首页添加了完整的PC端适配支持，提供更好的大屏幕用户体验。

## 主要功能

### 1. 智能平台检测
- 自动检测用户设备类型（移动端/PC端）
- 支持多平台：H5、微信小程序、百度小程序、头条小程序
- 动态响应窗口大小变化

### 2. 响应式布局
- **移动端**：保持原有3列宫格布局
- **PC端**：
  - 中等屏幕（768px-1200px）：4列布局
  - 大屏幕（>1200px）：6列布局
  - 最大容器宽度：1200px，居中显示

### 3. 组件优化

#### 搜索栏
- PC端：居中显示，优化字体大小
- 移动端：保持原有样式

#### 轮播图
- PC端：固定高度300px，添加圆角效果
- 移动端：保持原有自适应高度

#### 考试倒计时
- PC端：居中显示，优化内边距
- 移动端：保持原有样式

#### 宫格菜单
- PC端：动态列数，悬停效果，图标放大
- 移动端：保持3列布局

#### 文章列表
- PC端：优化卡片间距，悬停阴影效果
- 移动端：保持原有样式

### 4. 交互优化
- 按钮悬停效果：轻微上移 + 阴影
- 卡片悬停效果：上移 + 增强阴影
- 图标悬停效果：缩放动画

### 5. 视觉优化
- PC端页面背景：浅灰色 (#f8f9fa)
- 内容区域：白色背景 + 阴影
- 轮播图指示器：优化大小和颜色

## 技术实现

### 检测逻辑
```javascript
// H5平台
const userAgent = navigator.userAgent.toLowerCase();
isPCPlatform = !/mobile|android|iphone|ipad|phone/i.test(userAgent);

// 小程序平台
isPCPlatform = appPlatform === 21; // 微信小程序PC端
```

### 响应式计算
```javascript
computed: {
  isPC() {
    return this.isPCPlatform || this.windowWidth > 768;
  },
  gridColumns() {
    if (this.isPC) {
      return this.windowWidth > 1200 ? 6 : 4;
    }
    return 3;
  }
}
```

### CSS媒体查询
```css
@media screen and (min-width: 768px) {
  /* PC端样式 */
}

@media screen and (min-width: 1200px) {
  /* 大屏幕样式 */
}
```

## 兼容性
- ✅ H5浏览器
- ✅ 微信小程序
- ✅ 百度小程序  
- ✅ 头条小程序
- ✅ 移动端设备
- ✅ 平板设备
- ✅ PC端设备

## 使用说明
1. 无需额外配置，自动检测设备类型
2. 在PC端浏览器中打开应用即可看到优化效果
3. 支持浏览器窗口大小动态调整

## 注意事项
- 保持了移动端的完整功能和样式
- PC端优化不影响原有的移动端体验
- 所有交互功能在PC端和移动端均正常工作
