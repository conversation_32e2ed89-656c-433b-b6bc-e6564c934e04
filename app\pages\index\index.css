.menu-image {
	width: 56rpx;
	height: 56rpx;
	margin-bottom: 10rpx;
}


/* 考试倒计时样式 */
.exam-countdown {
	padding: 0;
	border-radius: 12rpx;
	margin: 0 20rpx;
	overflow: hidden;
}

.countdown-content {
	background: linear-gradient(135deg, #f0f8ff, #e6f2ff);
	border-radius: 12rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 100, 255, 0.15);
	padding: 0;
	position: relative;
	overflow: hidden;
}

.countdown-header {
	background-color: #0081ff;
	color: #ffffff;
	padding: 16rpx 24rpx;
	display: flex;
	align-items: center;
}

.countdown-title {
	font-size: 28rpx;
	font-weight: 500;
	margin-left: 10rpx;
}

.countdown-info {
	padding: 20rpx 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.countdown-date {
	font-size: 26rpx;
	color: #555;
}

.countdown-timer {
	display: flex;
	align-items: center;
}

.countdown-days {
	font-size: 40rpx;
	font-weight: bold;
	color: #0081ff;
	background-color: rgba(0, 129, 255, 0.1);
	padding: 6rpx 16rpx;
	border-radius: 8rpx;
	margin-right: 8rpx;
}

.countdown-unit {
	font-size: 28rpx;
	color: #555;
}

/* 新的宫格列表样式 */
.modern-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	padding: 20rpx 10rpx;
}

.modern-grid-item {
	width: 33.33%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
	transition: all 0.3s ease;
}

.modern-grid-item:active {
	transform: scale(0.95);
	opacity: 0.8;
}

.modern-grid-icon {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 10rpx;
	border-radius: 12rpx;
}

.modern-grid-text {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}

/* PC端适配样式 */
/* 主容器PC端适配 */
.pc-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
}

/* PC端搜索栏适配 */
@media screen and (min-width: 768px) {
	.cu-bar.search {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 20px;
	}

	.cu-bar.search .action {
		font-size: 16px !important;
		max-width: none !important;
		flex: 1;
		justify-content: center;
	}
}

/* PC端轮播图适配 */
@media screen and (min-width: 768px) {
	.screen-swiper {
		max-width: 1200px;
		margin: 0 auto;
		height: 300px;
		min-height: 300px;
	}

	.swiper-image {
		object-fit: cover;
		border-radius: 12px;
	}
}

/* PC端考试倒计时适配 */
@media screen and (min-width: 768px) {
	.exam-countdown {
		max-width: 1200px;
		margin: 14rpx auto 0;
		padding: 0 20px;
	}

	.countdown-content {
		border-radius: 16px;
	}

	.countdown-info {
		padding: 24px 32px;
	}

	.countdown-header {
		padding: 20px 32px;
	}
}

/* PC端宫格布局适配 */
@media screen and (min-width: 768px) {
	.modern-grid {
		max-width: 1200px;
		margin: 0 auto;
		padding: 30px 20px;
		justify-content: flex-start;
	}

	.modern-grid-item {
		width: 25%;
		padding: 30px 20px;
		transition: all 0.3s ease;
		border-radius: 12px;
		margin-bottom: 20px;
	}

	.modern-grid-item:hover {
		background-color: #f8f9fa;
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	}

	.modern-grid-icon {
		width: 80px;
		height: 80px;
		margin-bottom: 16px;
	}

	.modern-grid-text {
		font-size: 16px;
		font-weight: 600;
	}
}

/* 超大屏幕适配 */
@media screen and (min-width: 1200px) {
	.modern-grid-item {
		width: 16.666%;
	}
}

/* PC端文章列表适配 */
@media screen and (min-width: 768px) {
	.cu-card.article {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 20px;
	}

	.cu-card.article .cu-item {
		margin-bottom: 20px;
		border-radius: 12px;
		transition: all 0.3s ease;
	}

	.cu-card.article .cu-item:hover {
		transform: translateY(-2px);
		box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
	}

	.cu-card.article .content {
		padding: 24px;
	}

	.cu-card.article image {
		width: 150px;
		height: 150px;
		border-radius: 8px;
	}

	.cu-card.article .desc {
		margin-left: 20px;
		flex: 1;
	}

	.cu-card.article .text-df {
		font-size: 18px;
		line-height: 1.6;
		margin-bottom: 12px;
	}

	.cu-card.article .text-sm {
		font-size: 14px;
	}
}

/* PC端标题栏适配 */
@media screen and (min-width: 768px) {
	.cu-bar {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 20px;
	}

	.cu-bar .action.sub-title .text-xl {
		font-size: 24px;
	}

	.cu-bar .action.sub-title .text-ABC {
		font-size: 14px;
	}

	.cu-bar .cu-btn {
		padding: 12px 24px;
		font-size: 14px;
	}
}

/* PC端页面整体布局优化 */
@media screen and (min-width: 768px) {
	page {
		background-color: #f8f9fa;
	}

	/* 页面内容区域 */
	.pc-container {
		background-color: #ffffff;
		min-height: 100vh;
		box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
	}

	/* 优化加载状态 */
	.loading-container {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 60vh;
	}

	/* PC端按钮悬停效果 */
	.cu-btn:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 12px rgba(0, 129, 255, 0.3);
	}

	/* PC端卡片悬停效果 */
	.cu-item:hover {
		transform: translateY(-2px);
		box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
	}

	/* PC端图标悬停效果 */
	.modern-grid-item:hover .modern-grid-icon {
		transform: scale(1.1);
	}

	/* PC端轮播图指示器优化 */
	.screen-swiper .uni-swiper-dot {
		width: 12px;
		height: 12px;
		background-color: rgba(255, 255, 255, 0.5);
	}

	.screen-swiper .uni-swiper-dot-active {
		background-color: #0081ff;
	}
}
