.menu-image {
	width: 56rpx;
	height: 56rpx;
	margin-bottom: 10rpx;
}


/* 考试倒计时样式 */
.exam-countdown {
	padding: 0;
	border-radius: 12rpx;
	margin: 0 20rpx;
	overflow: hidden;
}

.countdown-content {
	background: linear-gradient(135deg, #f0f8ff, #e6f2ff);
	border-radius: 12rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 100, 255, 0.15);
	padding: 0;
	position: relative;
	overflow: hidden;
}

.countdown-header {
	background-color: #0081ff;
	color: #ffffff;
	padding: 16rpx 24rpx;
	display: flex;
	align-items: center;
}

.countdown-title {
	font-size: 28rpx;
	font-weight: 500;
	margin-left: 10rpx;
}

.countdown-info {
	padding: 20rpx 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.countdown-date {
	font-size: 26rpx;
	color: #555;
}

.countdown-timer {
	display: flex;
	align-items: center;
}

.countdown-days {
	font-size: 40rpx;
	font-weight: bold;
	color: #0081ff;
	background-color: rgba(0, 129, 255, 0.1);
	padding: 6rpx 16rpx;
	border-radius: 8rpx;
	margin-right: 8rpx;
}

.countdown-unit {
	font-size: 28rpx;
	color: #555;
}

/* 新的宫格列表样式 */
.modern-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	padding: 20rpx 10rpx;
}

.modern-grid-item {
	width: 33.33%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
	transition: all 0.3s ease;
}

.modern-grid-item:active {
	transform: scale(0.95);
	opacity: 0.8;
}

.modern-grid-icon {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 10rpx;
	border-radius: 12rpx;
}

.modern-grid-text {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}

/* PC端适配样式 - 充分利用屏幕空间 */
/* 主容器PC端适配 */
.pc-container {
	width: 95%;
	max-width: 2400px;
	min-width: 800px;
	margin: 0 auto;
	padding: 0 20px;
}

/* PC端搜索栏适配 */
@media screen and (min-width: 768px) {
	.cu-bar.search {
		width: 95%;
		max-width: 2400px;
		min-width: 800px;
		margin: 0 auto;
		padding: 0 20px;
	}

	.cu-bar.search .action {
		font-size: 16px !important;
		max-width: none !important;
		flex: 1;
		justify-content: center;
		padding: 12px 20px;
	}
}

/* PC端轮播图适配 */
@media screen and (min-width: 768px) {
	.screen-swiper {
		width: 95%;
		max-width: 2400px;
		min-width: 800px;
		margin: 0 auto;
		height: 400px;
		min-height: 400px;
	}

	.swiper-image {
		object-fit: cover;
		border-radius: 12px;
		width: 100%;
		height: 100%;
	}
}

/* 超大屏幕轮播图优化 */
@media screen and (min-width: 1600px) {
	.screen-swiper {
		height: 500px;
		min-height: 500px;
	}
}

/* PC端考试倒计时适配 */
@media screen and (min-width: 768px) {
	.exam-countdown {
		width: 95%;
		max-width: 2400px;
		min-width: 800px;
		margin: 14rpx auto 0;
		padding: 0 20px;
	}

	.countdown-content {
		border-radius: 16px;
		display: flex;
		flex-direction: column;
	}

	.countdown-info {
		padding: 24px 32px;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.countdown-header {
		padding: 20px 32px;
	}

	.countdown-date {
		font-size: 18px;
	}

	.countdown-days {
		font-size: 48px;
		padding: 8px 20px;
	}

	.countdown-unit {
		font-size: 18px;
	}
}

/* PC端宫格布局适配 - 动态列数 */
@media screen and (min-width: 768px) {
	.modern-grid {
		width: 95%;
		max-width: 2400px;
		min-width: 800px;
		margin: 0 auto;
		padding: 30px 20px;
		justify-content: flex-start;
		gap: 15px;
	}

	.modern-grid-item {
		/* 宽度通过JS动态设置 */
		min-width: 120px;
		max-width: 200px;
		flex: 1;
		padding: 25px 15px;
		transition: all 0.3s ease;
		border-radius: 12px;
		margin-bottom: 20px;
		box-sizing: border-box;
	}

	.modern-grid-item:hover {
		background-color: #f8f9fa;
		transform: translateY(-3px);
		box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
	}

	.modern-grid-icon {
		width: 64px;
		height: 64px;
		margin-bottom: 12px;
		transition: transform 0.3s ease;
	}

	.modern-grid-item:hover .modern-grid-icon {
		transform: scale(1.1);
	}

	.modern-grid-text {
		font-size: 14px;
		font-weight: 600;
		text-align: center;
		line-height: 1.4;
	}
}

/* 中等屏幕优化 */
@media screen and (min-width: 1200px) {
	.modern-grid-icon {
		width: 72px;
		height: 72px;
	}

	.modern-grid-text {
		font-size: 15px;
	}
}

/* 大屏幕优化 */
@media screen and (min-width: 1600px) {
	.modern-grid-icon {
		width: 80px;
		height: 80px;
	}

	.modern-grid-text {
		font-size: 16px;
	}
}

/* PC端文章列表适配 - 多列布局 */
@media screen and (min-width: 768px) {
	.cu-card.article {
		width: 95%;
		max-width: 2400px;
		min-width: 800px;
		margin: 0 auto;
		padding: 0 20px;
		display: flex;
		flex-wrap: wrap;
		gap: 20px;
	}

	.cu-card.article .cu-item {
		flex: 1;
		min-width: 350px;
		margin-bottom: 20px;
		border-radius: 12px;
		transition: all 0.3s ease;
		box-sizing: border-box;
	}

	.cu-card.article .cu-item:hover {
		transform: translateY(-3px);
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
	}

	.cu-card.article .content {
		padding: 24px;
		height: 100%;
		display: flex;
		flex-direction: column;
	}

	.cu-card.article image {
		width: 120px;
		height: 120px;
		border-radius: 8px;
		object-fit: cover;
	}

	.cu-card.article .desc {
		margin-left: 20px;
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.cu-card.article .text-df {
		font-size: 16px;
		line-height: 1.6;
		margin-bottom: 12px;
		font-weight: 500;
		color: #333;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.cu-card.article .text-sm {
		font-size: 13px;
		color: #666;
	}
}

/* 大屏幕文章列表优化 */
@media screen and (min-width: 1200px) {
	.cu-card.article .cu-item {
		min-width: 400px;
	}

	.cu-card.article .text-df {
		font-size: 17px;
	}

	.cu-card.article image {
		width: 140px;
		height: 140px;
	}
}

/* 超大屏幕文章列表优化 */
@media screen and (min-width: 1800px) {
	.cu-card.article .cu-item {
		min-width: 450px;
	}

	.cu-card.article .text-df {
		font-size: 18px;
	}

	.cu-card.article image {
		width: 160px;
		height: 160px;
	}
}

/* PC端标题栏适配 */
@media screen and (min-width: 768px) {
	.cu-bar {
		width: 95%;
		max-width: 2400px;
		min-width: 800px;
		margin: 0 auto;
		padding: 0 20px;
	}

	.cu-bar .action.sub-title .text-xl {
		font-size: 24px;
	}

	.cu-bar .action.sub-title .text-ABC {
		font-size: 14px;
	}

	.cu-bar .cu-btn {
		padding: 12px 24px;
		font-size: 14px;
	}
}

/* PC端页面整体布局优化 */
@media screen and (min-width: 768px) {
	page {
		background-color: #f8f9fa;
	}

	/* 页面内容区域 */
	.pc-container {
		background-color: #ffffff;
		min-height: 100vh;
		box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
	}

	/* 优化加载状态 */
	.loading-container {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 60vh;
	}

	/* PC端按钮悬停效果 */
	.cu-btn:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 12px rgba(0, 129, 255, 0.3);
	}

	/* PC端卡片悬停效果 */
	.cu-item:hover {
		transform: translateY(-2px);
		box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
	}

	/* PC端图标悬停效果 */
	.modern-grid-item:hover .modern-grid-icon {
		transform: scale(1.1);
	}

	/* PC端轮播图指示器优化 */
	.screen-swiper .uni-swiper-dot {
		width: 12px;
		height: 12px;
		background-color: rgba(255, 255, 255, 0.5);
	}

	.screen-swiper .uni-swiper-dot-active {
		background-color: #0081ff;
	}
}
