let that = null,
	app = getApp(),
	config = app.globalData.config,
	cache = config.storage,
	helper = app.globalData.helper;
export default {
	data() {
		return {
			images: [],
			appIsAudit: false,
			showWxGroup: false,
			showWxGroupText: '',
			appPlatform: app.globalData.appPlatform,
			showIndexShareName: '',
			articleList: [],
			currentCity: {},
			currentExam: {},
			currentProfession: {},
			menuList: [],
			examTime: {},
			isLoading: true,
			isPCPlatform: false,
			windowWidth: 0
		};
	},
	computed: {
		// 大屏检测 - 当屏幕宽度超过360rpx时启用响应式适配
		isLargeScreen() {
			// 将px转换为rpx进行判断，uni-app中1px约等于2rpx
			const screenWidthRpx = this.windowWidth * 2;
			return screenWidthRpx > 720; // 对应360rpx
		},

		// 动态计算宫格列数 - 根据屏幕宽度自适应
		gridColumns() {
			if (this.isLargeScreen) {
				// 大屏端根据屏幕宽度动态计算列数，每列最小宽度约300rpx
				const minItemWidthRpx = 300;
				const paddingRpx = 80; // 左右内边距
				const screenWidthRpx = this.windowWidth * 2; // px转rpx
				const availableWidthRpx = screenWidthRpx - paddingRpx;
				const columns = Math.floor(availableWidthRpx / minItemWidthRpx);
				return Math.max(4, Math.min(columns, 12)); // 最少4列，最多12列
			}
			return 3;
		},

		// 动态计算容器宽度 - 充分利用屏幕空间
		containerWidthRpx() {
			if (this.isLargeScreen) {
				// 大屏端使用95%的屏幕宽度
				const screenWidthRpx = this.windowWidth * 2; // px转rpx
				const containerWidthRpx = screenWidthRpx * 0.95;
				const minWidthRpx = 1600; // 最小宽度800px = 1600rpx
				const maxWidthRpx = 4800; // 最大宽度2400px = 4800rpx
				return Math.max(minWidthRpx, Math.min(containerWidthRpx, maxWidthRpx));
			}
			return 750; // 标准移动端宽度
		},

		// 计算文章列表列数
		articleColumns() {
			if (this.isLargeScreen) {
				const screenWidthRpx = this.windowWidth * 2;
				// 大屏幕显示2列文章，超大屏幕显示3列
				if (screenWidthRpx > 3600) return 3; // 1800px
				if (screenWidthRpx > 2400) return 2; // 1200px
				return 1;
			}
			return 1;
		}
	},
	onLoad(options) {
		app.globalData.showShareMenu();
		that = this;
		that.initPlatformInfo();
	},
	onShow(options) {
		that.getHomeData();
		that.initIndexExamData();
	},
	onUnload() {
		// 移除窗口大小变化监听
		// #ifdef H5
		if (typeof window !== 'undefined') {
			window.removeEventListener('resize', that.handleResize);
		}
		// #endif
	},
	onShareAppMessage() {
		return app.globalData.getShareConfig();
	},
	methods: {
		/**
		 * 初始化平台信息
		 */
		initPlatformInfo() {
			// 获取系统信息
			const systemInfo = uni.getSystemInfoSync();
			that.windowWidth = systemInfo.windowWidth;

			// 检测是否为PC平台
			// #ifdef H5
			const userAgent = navigator.userAgent.toLowerCase();
			that.isPCPlatform = !/mobile|android|iphone|ipad|phone/i.test(userAgent);

			// 添加窗口大小变化监听
			if (typeof window !== 'undefined') {
				that.handleResize = () => {
					const newSystemInfo = uni.getSystemInfoSync();
					that.windowWidth = newSystemInfo.windowWidth;
				};
				window.addEventListener('resize', that.handleResize);
			}
			// #endif

			// #ifdef MP-WEIXIN
			that.isPCPlatform = that.appPlatform === 21; // 微信小程序PC端
			// #endif

			// #ifdef MP-BAIDU
			that.isPCPlatform = that.appPlatform === 41; // 百度小程序PC端
			// #endif

			// #ifdef MP-TOUTIAO
			that.isPCPlatform = that.appPlatform === 51; // 头条小程序PC端
			// #endif
		},

		/**
		 * 获取轮播图样式
		 */
		getSwiperStyle() {
			if (!this.isLargeScreen) {
				return 'min-height: 0;';
			}

			// 大屏端统一使用rpx单位
			const containerWidth = this.containerWidthRpx;
			return `min-height: 800rpx; width: ${containerWidth}rpx; margin: 28rpx auto 0;`;
		},

		/**
		 * 获取轮播图图片样式
		 */
		getSwiperImageStyle() {
			if (!this.isLargeScreen) {
				return '';
			}

			// 大屏端统一样式
			return 'border-radius: 24rpx;';
		},

		/**
		 * 获取倒计时样式
		 */
		getCountdownStyle() {
			if (!this.isLargeScreen) {
				return 'margin-top: 14rpx;';
			}

			// 大屏端统一样式
			const containerWidth = this.containerWidthRpx;
			return `margin-top: 28rpx; width: ${containerWidth}rpx; margin-left: auto; margin-right: auto;`;
		},

		/**
		 * 获取标题栏样式
		 */
		getBarStyle() {
			if (!this.isLargeScreen) {
				return '';
			}

			// 大屏端统一样式
			const containerWidth = this.containerWidthRpx;
			return `width: ${containerWidth}rpx; margin: 28rpx auto 0;`;
		},

		/**
		 * 获取宫格容器样式
		 */
		getGridStyle() {
			if (!this.isLargeScreen) {
				return '';
			}

			// 大屏端统一样式
			const containerWidth = this.containerWidthRpx;
			return `width: ${containerWidth}rpx; margin: 0 auto;`;
		},

		/**
		 * 获取宫格项目样式
		 */
		getGridItemStyle() {
			if (!this.isLargeScreen) {
				return '';
			}

			// 大屏端统一样式
			return `width: calc(${100/this.gridColumns}% - 30rpx);`;
		},

		/**
		 * 获取新闻标题栏样式
		 */
		getNewsBarStyle() {
			if (!this.isLargeScreen) {
				return 'margin-bottom: 3rpx;';
			}

			// 大屏端统一样式
			const containerWidth = this.containerWidthRpx;
			return `margin-bottom: 6rpx; width: ${containerWidth}rpx; margin-left: auto; margin-right: auto; margin-top: 28rpx;`;
		},

		/**
		 * 获取文章列表样式
		 */
		getArticleStyle() {
			if (!this.isLargeScreen) {
				return '';
			}

			// 大屏端统一样式
			const containerWidth = this.containerWidthRpx;
			return `width: ${containerWidth}rpx; margin: 0 auto;`;
		},

		/**
		 * 通用导航方法
		 * @param {String} url 完整的导航地址
		 */
		goTo(url) {
			uni.navigateTo({url});
		},
		
		checkAppIsAudit() {
			that.appIsAudit = app.globalData.checkAppIsAudit();
		},
		initIndexExamData() {
			that.currentCity = cache.getCurrentCityData();
			that.currentExam = cache.getCurrentExamData();
			that.currentProfession = cache.getCurrentProfessionData();
			that.changeExamInfo();
		},
		async changeExamInfo() {
			let exam_id = helper.variableDefalut(that.currentExam.id, 0),
				region_id = helper.variableDefalut(that.currentCity.id, 0),
				profession_id = helper.variableDefalut(that.currentProfession.id, 0);
			if (region_id == 0) {
				that.goTo('city/city?first_visit=1');
				return;
			}
			if (profession_id && app.globalData.isLogin) {
				let user = cache.getUserInfoData(),
					user_profession_id = helper
					.variableDefalut(user.profession_id, 0);
				if (user_profession_id != profession_id) {
					await app.globalData.service.userUpdate({
						exam_id: exam_id,
						region_id: region_id,
						profession_id: profession_id
					});
					user.profession_id = profession_id;
					cache.setUserInfoData(user);
				}
			}
		},
		
		/**
		 * 获取首页数据
		 */
		getHomeData() {
			that.isLoading = true;
			// 优先加载缓存
			let time = app.globalData.getTimestamp();
			let cacheHomeData = wx.getStorageSync(cache.homeKey)
			console.log(cacheHomeData, time);
			if (cacheHomeData && cacheHomeData.expire >= time) {
				that.images = cacheHomeData.swiper;
				that.menuList = cacheHomeData.menuList
				that.articleList = cacheHomeData.articleList;
				that.showWxGroup = cacheHomeData.appConfig.showWxGroup
				that.showWxGroupText = cacheHomeData.appConfig.showWxGroupText
				that.showIndexShareName = cacheHomeData.appConfig.showIndexShareName
				// 从缓存中获取考试时间数据
				if (cacheHomeData.examTime) {
					that.examTime = cacheHomeData.examTime;
				}
				that.isLoading = false;
				return;
			}

			// 加载接口数据
			let cacheTime = 0;
			app.globalData.server
				.getRequest('home', {})
				.then((res) => {
					that.images = res.data.swiper;
					that.menuList = res.data.menuList;
					that.articleList = res.data.articleList;
					that.showWxGroup = res.data.appConfig.showWxGroup;
					that.showWxGroupText = res.data.appConfig.showWxGroupText;
					that.showIndexShareName = res.data.appConfig.showIndexShareName;
					
					// 获取考试时间数据
					if (res.data.examTime) {
						that.examTime = res.data.examTime;
					}
					
					res.data.expire = time + cacheTime;
					cache.setHomeData(res.data);
					that.checkAppIsAudit();
					that.isLoading = false;
				})
				.catch((e) => {
					console.log(e)
					app.showToast('获取首页数据失败');
					that.isLoading = false;
				});
		}
	}
};