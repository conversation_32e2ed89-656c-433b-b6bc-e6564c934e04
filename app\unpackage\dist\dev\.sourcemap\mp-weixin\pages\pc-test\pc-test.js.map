{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/pc-test/pc-test.vue?1a46", "webpack:///D:/桌面/thinker/app/pages/pc-test/pc-test.vue?d396", "webpack:///D:/桌面/thinker/app/pages/pc-test/pc-test.vue?7120", "webpack:///D:/桌面/thinker/app/pages/pc-test/pc-test.vue?7ff9", "uni-app:///pages/pc-test/pc-test.vue", "webpack:///D:/桌面/thinker/app/pages/pc-test/pc-test.vue?c8c6", "webpack:///D:/桌面/thinker/app/pages/pc-test/pc-test.vue?5efa"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "app", "data", "isPCPlatform", "windowWidth", "computed", "isLargeScreen", "gridColumns", "containerWidthRpx", "onLoad", "that", "onUnload", "methods", "initPlatformInfo", "getPlatformText", "goBack", "uni"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACqC;;;AAG3F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA2pB,CAAgB,ypBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+C/qB;EACAC;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;;MACA;IACA;EACA;EACAC;IACAC;IACAA;EACA;EACAC;IACA;EAAA,CAMA;EACAC;IACA;AACA;AACA;IACAC;MACA;MACA;MACAH;;MAEA;;MAgBA;MACAA;IAYA;IAEAI;MACA;QACA;MACA;MAOA;MAWA;IACA;IAEAC;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1KA;AAAA;AAAA;AAAA;AAA49B,CAAgB,s7BAAG,EAAC,C;;;;;;;;;;;ACAh/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pc-test/pc-test.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pc-test/pc-test.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./pc-test.vue?vue&type=template&id=1bd04540&scoped=true&\"\nvar renderjs\nimport script from \"./pc-test.vue?vue&type=script&lang=js&\"\nexport * from \"./pc-test.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pc-test.vue?vue&type=style&index=0&id=1bd04540&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1bd04540\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pc-test/pc-test.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-test.vue?vue&type=template&id=1bd04540&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-test.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-test.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view class=\"test-container\">\n\t\t\t<view class=\"test-header\">\n\t\t\t\t<text class=\"test-title\">PC端适配测试页面</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"test-info\">\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">当前模式：</text>\n\t\t\t\t\t<text class=\"info-value\">{{ isLargeScreen ? '大屏模式' : '移动端模式' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">窗口宽度：</text>\n\t\t\t\t\t<text class=\"info-value\">{{ windowWidth }}px ({{ windowWidth * 2 }}rpx)</text>\n\t\t\t</view>\n\t\t\t<view class=\"info-item\">\n\t\t\t\t<text class=\"info-label\">触发阈值：</text>\n\t\t\t\t<text class=\"info-value\">360rpx (720rpx)</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">宫格列数：</text>\n\t\t\t\t\t<text class=\"info-value\">{{ gridColumns }}列</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t<text class=\"info-label\">容器宽度：</text>\n\t\t\t\t\t<text class=\"info-value\">{{ containerWidthRpx }}rpx</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"test-grid\" :style=\"isLargeScreen ? `width: ${containerWidthRpx}rpx; margin: 0 auto;` : ''\">\n\t\t\t\t<view class=\"grid-item\" v-for=\"n in 12\" :key=\"n\"\n\t\t\t\t\t:style=\"isLargeScreen ? `width: ${100/gridColumns}%;` : 'width: 33.33%;'\">\n\t\t\t\t\t<view class=\"grid-content\">\n\t\t\t\t\t\t<text>项目 {{ n }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"test-buttons\">\n\t\t\t\t<button class=\"test-btn\" @click=\"goBack\">返回首页</button>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nlet that = null,\n\tapp = getApp();\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tisPCPlatform: false,\n\t\t\twindowWidth: 0\n\t\t};\n\t},\n\tcomputed: {\n\t\t// 大屏检测 - 当屏幕宽度超过360rpx时启用响应式适配\n\t\tisLargeScreen() {\n\t\t\t// 将px转换为rpx进行判断，uni-app中1px约等于2rpx\n\t\t\tconst screenWidthRpx = this.windowWidth * 2;\n\t\t\treturn screenWidthRpx > 720; // 360rpx * 2 = 720\n\t\t},\n\n\t\t// 动态计算宫格列数 - 根据屏幕宽度自适应\n\t\tgridColumns() {\n\t\t\tif (this.isLargeScreen) {\n\t\t\t\t// 大屏端根据屏幕宽度动态计算列数，每列最小宽度约300rpx\n\t\t\t\tconst minItemWidthRpx = 300;\n\t\t\t\tconst paddingRpx = 80; // 左右内边距\n\t\t\t\tconst screenWidthRpx = this.windowWidth * 2; // px转rpx\n\t\t\t\tconst availableWidthRpx = screenWidthRpx - paddingRpx;\n\t\t\t\tconst columns = Math.floor(availableWidthRpx / minItemWidthRpx);\n\t\t\t\treturn Math.max(4, Math.min(columns, 10)); // 最少4列，最多10列\n\t\t\t}\n\t\t\treturn 3;\n\t\t},\n\n\t\t// 动态计算容器宽度 - 充分利用屏幕空间\n\t\tcontainerWidthRpx() {\n\t\t\tif (this.isLargeScreen) {\n\t\t\t\t// 大屏端使用95%的屏幕宽度\n\t\t\t\tconst screenWidthRpx = this.windowWidth * 2; // px转rpx\n\t\t\t\tconst containerWidthRpx = Math.floor(screenWidthRpx * 0.95);\n\t\t\t\treturn Math.max(1400, Math.min(containerWidthRpx, 4800)); // 最小1400rpx，最大4800rpx\n\t\t\t}\n\t\t\treturn 750; // 标准移动端宽度\n\t\t}\n\t},\n\tonLoad() {\n\t\tthat = this;\n\t\tthat.initPlatformInfo();\n\t},\n\tonUnload() {\n\t\t// 移除窗口大小变化监听\n\t\t// #ifdef H5\n\t\tif (typeof window !== 'undefined') {\n\t\t\twindow.removeEventListener('resize', that.handleResize);\n\t\t}\n\t\t// #endif\n\t},\n\tmethods: {\n\t\t/**\n\t\t * 初始化平台信息\n\t\t */\n\t\tinitPlatformInfo() {\n\t\t\t// 获取系统信息\n\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\tthat.windowWidth = systemInfo.windowWidth;\n\t\t\t\n\t\t\t// 检测是否为PC平台\n\t\t\t// #ifdef H5\n\t\t\tconst userAgent = navigator.userAgent.toLowerCase();\n\t\t\tthat.isPCPlatform = !/mobile|android|iphone|ipad|phone/i.test(userAgent);\n\t\t\t\n\t\t\t// 添加窗口大小变化监听\n\t\t\tif (typeof window !== 'undefined') {\n\t\t\t\tthat.handleResize = () => {\n\t\t\t\t\tconst newSystemInfo = uni.getSystemInfoSync();\n\t\t\t\t\tthat.windowWidth = newSystemInfo.windowWidth;\n\t\t\t\t};\n\t\t\t\twindow.addEventListener('resize', that.handleResize);\n\t\t\t}\n\t\t\t// #endif\n\t\t\t\n\t\t\t// #ifdef MP-WEIXIN\n\t\t\tconst appPlatform = app.globalData.appPlatform;\n\t\t\tthat.isPCPlatform = appPlatform === 21; // 微信小程序PC端\n\t\t\t// #endif\n\n\t\t// #ifdef MP-BAIDU\n\t\tconst appPlatform = app.globalData.appPlatform;\n\t\tthat.isPCPlatform = appPlatform === 41; // 百度小程序PC端\n\t\t// #endif\n\n\t\t// #ifdef MP-TOUTIAO\n\t\tconst appPlatform = app.globalData.appPlatform;\n\t\tthat.isPCPlatform = appPlatform === 51; // 头条小程序PC端\n\t\t// #endif\n\t\t},\n\t\t\n\t\tgetPlatformText() {\n\t\tif (!this.isLargeScreen) {\n\t\t\treturn '移动端模式';\n\t\t}\n\n\t\t// #ifdef H5\n\t\treturn '大屏模式 (Web)';\n\t\t// #endif\n\n\t\t// #ifdef MP-WEIXIN\n\t\treturn '大屏模式 (微信小程序)';\n\t\t// #endif\n\n\t\t// #ifdef MP-BAIDU\n\t\treturn '大屏模式 (百度小程序)';\n\t\t// #endif\n\n\t\t// #ifdef MP-TOUTIAO\n\t\treturn '大屏模式 (头条小程序)';\n\t\t// #endif\n\n\t\treturn '大屏模式';\n\t},\n\n\tgoBack() {\n\t\t\tuni.navigateBack();\n\t\t}\n\t}\n};\n</script>\n\n<style scoped>\n.test-container {\n\tpadding: 20rpx;\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n}\n\n.test-header {\n\ttext-align: center;\n\tpadding: 40rpx 0;\n\tbackground-color: #0081ff;\n\tcolor: white;\n\tborder-radius: 12rpx;\n\tmargin-bottom: 30rpx;\n}\n\n.test-title {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n}\n\n.test-info {\n\tbackground-color: white;\n\tborder-radius: 12rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 30rpx;\n}\n\n.info-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1rpx solid #eee;\n}\n\n.info-item:last-child {\n\tborder-bottom: none;\n}\n\n.info-label {\n\tfont-size: 28rpx;\n\tcolor: #666;\n}\n\n.info-value {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n}\n\n.test-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tbackground-color: white;\n\tborder-radius: 12rpx;\n\tpadding: 20rpx;\n\tmargin-bottom: 30rpx;\n}\n\n.grid-item {\n\tpadding: 10rpx;\n\tbox-sizing: border-box;\n}\n\n.grid-content {\n\tbackground-color: #f0f8ff;\n\tborder: 2rpx solid #0081ff;\n\tborder-radius: 8rpx;\n\tpadding: 30rpx 20rpx;\n\ttext-align: center;\n\ttransition: all 0.3s ease;\n}\n\n.grid-content:hover {\n\tbackground-color: #0081ff;\n\tcolor: white;\n\ttransform: translateY(-2rpx);\n}\n\n.test-buttons {\n\ttext-align: center;\n}\n\n.test-btn {\n\tbackground-color: #0081ff;\n\tcolor: white;\n\tborder: none;\n\tborder-radius: 8rpx;\n\tpadding: 20rpx 40rpx;\n\tfont-size: 28rpx;\n}\n\n/* PC端适配 */\n@media screen and (min-width: 768px) {\n\t.test-container {\n\t\tmax-width: 1200px;\n\t\tmargin: 0 auto;\n\t\tpadding: 40px 20px;\n\t}\n\t\n\t.test-title {\n\t\tfont-size: 24px;\n\t}\n\t\n\t.info-label, .info-value {\n\t\tfont-size: 16px;\n\t}\n\t\n\t.test-btn {\n\t\tfont-size: 16px;\n\t\tpadding: 12px 24px;\n\t}\n\t\n\t.test-btn:hover {\n\t\tbackground-color: #0066cc;\n\t\ttransform: translateY(-1px);\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-test.vue?vue&type=style&index=0&id=1bd04540&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-test.vue?vue&type=style&index=0&id=1bd04540&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753606151521\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}