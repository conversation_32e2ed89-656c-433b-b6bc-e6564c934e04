<template>
	<view>
		<view class="test-container">
			<view class="test-header">
				<text class="test-title">PC端适配测试页面</text>
			</view>
			
			<view class="test-info">
				<view class="info-item">
					<text class="info-label">当前平台：</text>
					<text class="info-value">{{ isPC ? 'PC端' : '移动端' }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">窗口宽度：</text>
					<text class="info-value">{{ windowWidth }}px</text>
				</view>
				<view class="info-item">
					<text class="info-label">宫格列数：</text>
					<text class="info-value">{{ gridColumns }}列</text>
				</view>
				<view class="info-item">
					<text class="info-label">容器最大宽度：</text>
					<text class="info-value">{{ containerMaxWidth }}</text>
				</view>
			</view>
			
			<view class="test-grid" :style="isPC ? `max-width: ${containerMaxWidth}; margin: 0 auto;` : ''">
				<view class="grid-item" v-for="n in 12" :key="n" 
					:style="isPC ? `width: ${100/gridColumns}%;` : 'width: 33.33%;'">
					<view class="grid-content">
						<text>项目 {{ n }}</text>
					</view>
				</view>
			</view>
			
			<view class="test-buttons">
				<button class="test-btn" @click="goBack">返回首页</button>
			</view>
		</view>
	</view>
</template>

<script>
let that = null,
	app = getApp();

export default {
	data() {
		return {
			isPCPlatform: false,
			windowWidth: 0
		};
	},
	computed: {
		// PC端检测
		isPC() {
			return this.isPCPlatform || this.windowWidth > 768;
		},
		// 动态计算宫格列数
		gridColumns() {
			if (this.isPC) {
				return this.windowWidth > 1200 ? 6 : 4;
			}
			return 3;
		},
		// 动态计算容器最大宽度
		containerMaxWidth() {
			if (this.isPC) {
				return this.windowWidth > 1200 ? '1200px' : '900px';
			}
			return '100%';
		}
	},
	onLoad() {
		that = this;
		that.initPlatformInfo();
	},
	onUnload() {
		// 移除窗口大小变化监听
		// #ifdef H5
		if (typeof window !== 'undefined') {
			window.removeEventListener('resize', that.handleResize);
		}
		// #endif
	},
	methods: {
		/**
		 * 初始化平台信息
		 */
		initPlatformInfo() {
			// 获取系统信息
			const systemInfo = uni.getSystemInfoSync();
			that.windowWidth = systemInfo.windowWidth;
			
			// 检测是否为PC平台
			// #ifdef H5
			const userAgent = navigator.userAgent.toLowerCase();
			that.isPCPlatform = !/mobile|android|iphone|ipad|phone/i.test(userAgent);
			
			// 添加窗口大小变化监听
			if (typeof window !== 'undefined') {
				that.handleResize = () => {
					const newSystemInfo = uni.getSystemInfoSync();
					that.windowWidth = newSystemInfo.windowWidth;
				};
				window.addEventListener('resize', that.handleResize);
			}
			// #endif
			
			// #ifdef MP-WEIXIN
			const appPlatform = app.globalData.appPlatform;
			that.isPCPlatform = appPlatform === 21; // 微信小程序PC端
			// #endif
		},
		
		goBack() {
			uni.navigateBack();
		}
	}
};
</script>

<style scoped>
.test-container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.test-header {
	text-align: center;
	padding: 40rpx 0;
	background-color: #0081ff;
	color: white;
	border-radius: 12rpx;
	margin-bottom: 30rpx;
}

.test-title {
	font-size: 36rpx;
	font-weight: bold;
}

.test-info {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.info-item {
	display: flex;
	justify-content: space-between;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #eee;
}

.info-item:last-child {
	border-bottom: none;
}

.info-label {
	font-size: 28rpx;
	color: #666;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

.test-grid {
	display: flex;
	flex-wrap: wrap;
	background-color: white;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 30rpx;
}

.grid-item {
	padding: 10rpx;
	box-sizing: border-box;
}

.grid-content {
	background-color: #f0f8ff;
	border: 2rpx solid #0081ff;
	border-radius: 8rpx;
	padding: 30rpx 20rpx;
	text-align: center;
	transition: all 0.3s ease;
}

.grid-content:hover {
	background-color: #0081ff;
	color: white;
	transform: translateY(-2rpx);
}

.test-buttons {
	text-align: center;
}

.test-btn {
	background-color: #0081ff;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

/* PC端适配 */
@media screen and (min-width: 768px) {
	.test-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 40px 20px;
	}
	
	.test-title {
		font-size: 24px;
	}
	
	.info-label, .info-value {
		font-size: 16px;
	}
	
	.test-btn {
		font-size: 16px;
		padding: 12px 24px;
	}
	
	.test-btn:hover {
		background-color: #0066cc;
		transform: translateY(-1px);
	}
}
</style>
