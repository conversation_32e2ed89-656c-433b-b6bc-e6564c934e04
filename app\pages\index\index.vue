<template>
	<view>
		<back :showBackText="false" :showBackIcon="false" :showBackLeft="false" :showHomeIcon="false"
			customClass="bg-gradual-blue text-white" title="首页"></back>

		<!-- Page content, hidden with v-show instead of v-if to preserve structure -->
		<view v-show="!isLoading" :class="{'pc-container': isPC}">
			<view class="cu-bar bg-white search">
				<view class="action" @tap="goTo('city/city?first_visit=1')" :style="isPC ? 'font-size: 16px;' : 'font-size: 27rpx; max-width: 33.3333%'">
					<text class="cuIcon-location text-blue" style="margin: 0"></text>
					<text class="text-blue text-cut">{{ currentCity.name }}</text>
				</view>
				<view class="action" @tap="goTo('exam/exam')" :style="isPC ? 'font-size: 16px;' : 'font-size: 27rpx; max-width: 33.3333%'">
					<text class="cuIcon-group text-blue" style="margin-right: 6rpx"></text>
					<text class="text-blue text-cut">{{ currentExam.name }}</text>
				</view>
				<view class="action" @tap="goTo('profession/profession')" :style="isPC ? 'font-size: 16px;' : 'font-size: 27rpx; max-width: 33.3333%'">
					<text class="cuIcon-read text-blue" style="margin-right: 6rpx"></text>
					<text class="text-blue text-cut">{{ currentProfession.name || '选择专业' }}</text>
				</view>
			</view>
			<swiper class="swiper screen-swiper square-dot margin-top-xs" :indicator-dots="true" :circular="true"
				:style="isPC ? 'min-height: 300px; max-width: ' + containerMaxWidth + '; margin: 14rpx auto 0;' : 'min-height: 0;'"
				:autoplay="true" interval="5000" duration="500">
				<swiper-item @tap="goTo(item.target_url)" v-for="(item, index) in images" :key="index">
					<image :src="item.img_url" class="swiper-image" :style="isPC ? 'border-radius: 12px;' : ''"></image>
				</swiper-item>
			</swiper>


			<view v-if="!appIsAudit && showIndexShareName && (appPlatform == 20 || appPlatform == 21)"
				class="cu-bar bg-white margin-top-xs no-border" style="font-size: 50rpx;display:none">
				<view class="action" style="font-size: 48rpx;">
					<text class="cuIcon-notificationfill text-blue" style="font-size: 50rpx;"></text> <text
						class="text-blue text-df">{{ showIndexShareName }}</text>
				</view>
				<view class="action">
					<button class="cu-btn margin-tb-sm bg-blue shadow" open-type="share">分享<text class="cuIcon-share"
							style="margin-left: 6rpx;"></text></button>
				</view>
			</view>

			<!-- 考试倒计时提示 -->
			<view v-if="examTime.show" class="exam-countdown"
				:style="isPC ? `margin-top: 14rpx; max-width: ${containerMaxWidth}; margin-left: auto; margin-right: auto;` : 'margin-top: 14rpx;'">
				<view class="countdown-content">
					<view class="countdown-header">
						<text class="cuIcon-time text-blue"></text>
						<text class="countdown-title">考试倒计时</text>
					</view>
					<view class="countdown-info">
						<view class="countdown-date">考试日期：{{ examTime.exam_date }}</view>
						<view class="countdown-timer">
							<text class="countdown-unit">还剩</text>
							<text class="countdown-days">{{ examTime.exam_remain }}</text>
							<text class="countdown-unit">天</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 新的宫格列表设计 -->
			<view class="cu-bar bg-white margin-top-xs" :style="isPC ? `max-width: ${containerMaxWidth}; margin: 14rpx auto 0;` : ''">
				<view class="action sub-title">
					<text class="text-xl text-bold text-blue text-shadow">学习中心</text>
					<text class="text-ABC text-blue">Study</text>
				</view>

				<view class="action">
					<button class="cu-btn margin-tb-sm bg-blue shadow" @tap="goTo('/pages/pc-test/pc-test')" v-if="isPC">PC测试<text class="cuIcon-settings"
							style="margin-left: 6rpx;"></text></button>
					<button class="cu-btn margin-tb-sm bg-blue shadow" open-type="share" v-else>分享<text class="cuIcon-share"
							style="margin-left: 6rpx;"></text></button>
				</view>
			</view>
			<view class="modern-grid bg-white radius shadow-warp" :style="isPC ? `max-width: ${containerMaxWidth}; margin: 0 auto;` : ''">
				<view @tap="goTo(item.page_uri)" class="modern-grid-item" v-for="(item, index) in menuList" :key="index"
					:style="isPC ? `width: ${100/gridColumns}%;` : ''">
					<image :src="item.icon" mode="aspectFit" class="modern-grid-icon"></image>
					<text class="modern-grid-text">{{ item.name }}</text>
				</view>
			</view>
			
			<view class="cu-bar bg-white margin-top-xs"
				:style="isPC ? `margin-bottom: 3rpx; max-width: ${containerMaxWidth}; margin-left: auto; margin-right: auto; margin-top: 14rpx;` : 'margin-bottom: 3rpx;'"
				v-if="!appIsAudit">
				<view class="action sub-title">
					<text class="text-xl text-bold text-blue text-shadow">最新资讯</text>
					<text class="text-ABC text-blue">News</text>
				</view>
				<view class="action" @tap="goTo('./article/list')"><button class="cu-btn bg-blue margin-tb-sm shadow"
						data-target="Modal">更多<text class="cuIcon-moreandroid" style="margin-left: 5rpx;"></text></button>
				</view>
			</view>
			<view class="cu-card article no-card solid-bottom" :style="isPC ? `max-width: ${containerMaxWidth}; margin: 0 auto;` : ''"
				v-if="!appIsAudit || (appPlatform != 20 && appPlatform != 21)">
				<view class="cu-item shadow" @tap="goTo(`./article/detail?id=${item.id}`)"
					v-for="(item, index) in articleList" :key="index">
					<view class="content">
						<view>
							<image :src="item.thumb" mode="aspectFit" class="radius" style="height: 120rpx;width: 120rpx;">
							</image>
						</view>
						<view class="desc" style="justify-content: space-around;margin-left: 10rpx;">
							<view class="text-black text-df">{{ item.title }}</view>
							<view class="flex justify-between">
								<view class="text-gray text-sm">{{ item.cate_name }} · {{ item.create_date }}</view>
								<view class="text-gray text-sm padding-right text-shadow">{{ item.page_view }} 阅读
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<style src="./index.css"></style>
<script src="./index.js"></script>